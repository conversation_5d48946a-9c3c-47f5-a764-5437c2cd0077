import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/models/cart_item.dart';
import 'package:pos_app/screens/invoice_preview_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

class InvoiceActionsWidget extends StatelessWidget {
  final String invoiceId;
  final List<CartItem> items;
  final double totalAmount;
  final String customerName;
  final DateTime date;
  final String? paymentMethod;
  final bool isCompact;
  final MainAxisAlignment alignment;

  const InvoiceActionsWidget({
    super.key,
    required this.invoiceId,
    required this.items,
    required this.totalAmount,
    required this.customerName,
    required this.date,
    this.paymentMethod,
    this.isCompact = false,
    this.alignment = MainAxisAlignment.spaceEvenly,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<LocaleProvider, CurrencyProvider>(
      builder: (context, localeProvider, currencyProvider, _) {
        final isRTL = localeProvider.isRTL;
        
        if (isCompact) {
          return _buildCompactActions(context, isRTL, currencyProvider);
        } else {
          return _buildFullActions(context, isRTL, currencyProvider);
        }
      },
    );
  }

  Widget _buildFullActions(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          isRTL ? 'إجراءات الفاتورة' : 'Invoice Actions',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // Preview Invoice Button
            Expanded(
              child: _buildActionButton(
                context,
                icon: FontAwesomeIcons.eye,
                label: isRTL ? 'معاينة' : 'Preview',
                color: Colors.blue,
                onPressed: () => _previewInvoice(context, isRTL, currencyProvider),
              ),
            ),
            const SizedBox(width: 8),
            // Print Invoice Button
            Expanded(
              child: _buildActionButton(
                context,
                icon: FontAwesomeIcons.print,
                label: isRTL ? 'طباعة' : 'Print',
                color: Colors.purple,
                onPressed: () => _printInvoice(context, isRTL, currencyProvider),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Share via WhatsApp Button
            Expanded(
              child: _buildActionButton(
                context,
                icon: FontAwesomeIcons.whatsapp,
                label: 'WhatsApp',
                color: const Color(0xFF25D366),
                onPressed: () => _shareViaWhatsApp(context, isRTL, currencyProvider),
              ),
            ),
            const SizedBox(width: 8),
            // Share via Email Button
            Expanded(
              child: _buildActionButton(
                context,
                icon: FontAwesomeIcons.envelope,
                label: isRTL ? 'إيميل' : 'Email',
                color: Colors.red,
                onPressed: () => _shareViaEmail(context, isRTL, currencyProvider),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactActions(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) {
    return Row(
      mainAxisAlignment: alignment,
      children: [
        _buildCompactActionButton(
          context,
          icon: FontAwesomeIcons.eye,
          tooltip: isRTL ? 'معاينة' : 'Preview',
          color: Colors.blue,
          onPressed: () => _previewInvoice(context, isRTL, currencyProvider),
        ),
        _buildCompactActionButton(
          context,
          icon: FontAwesomeIcons.print,
          tooltip: isRTL ? 'طباعة' : 'Print',
          color: Colors.purple,
          onPressed: () => _printInvoice(context, isRTL, currencyProvider),
        ),
        _buildCompactActionButton(
          context,
          icon: FontAwesomeIcons.whatsapp,
          tooltip: 'WhatsApp',
          color: const Color(0xFF25D366),
          onPressed: () => _shareViaWhatsApp(context, isRTL, currencyProvider),
        ),
        _buildCompactActionButton(
          context,
          icon: FontAwesomeIcons.envelope,
          tooltip: isRTL ? 'إيميل' : 'Email',
          color: Colors.red,
          onPressed: () => _shareViaEmail(context, isRTL, currencyProvider),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: FaIcon(icon, size: 16),
      label: Text(
        label,
        style: GoogleFonts.cairo(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildCompactActionButton(
    BuildContext context, {
    required IconData icon,
    required String tooltip,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: FaIcon(icon, size: 18),
      tooltip: tooltip,
      color: color,
      style: IconButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        padding: const EdgeInsets.all(8),
      ),
    );
  }

  void _previewInvoice(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoicePreviewScreen(
          invoiceId: invoiceId,
          items: items,
          totalAmount: totalAmount,
          customerName: customerName,
          date: date,
        ),
      ),
    );
  }

  void _printInvoice(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) {
    // Show print options dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isRTL ? 'خيارات الطباعة' : 'Print Options',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const FaIcon(FontAwesomeIcons.print, color: Colors.blue),
              title: Text(
                isRTL ? 'طباعة عادية' : 'Regular Print',
                style: GoogleFonts.cairo(),
              ),
              onTap: () {
                Navigator.pop(context);
                _performPrint(context, 'regular', isRTL, currencyProvider);
              },
            ),
            ListTile(
              leading: const FaIcon(FontAwesomeIcons.bluetooth, color: Colors.green),
              title: Text(
                isRTL ? 'طباعة حرارية' : 'Thermal Print',
                style: GoogleFonts.cairo(),
              ),
              onTap: () {
                Navigator.pop(context);
                _performPrint(context, 'thermal', isRTL, currencyProvider);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _performPrint(BuildContext context, String printType, bool isRTL, CurrencyProvider currencyProvider) {
    // Generate invoice text
    final invoiceText = _generateInvoiceText(isRTL, currencyProvider);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL 
            ? 'تم إرسال الفاتورة للطباعة ($printType)'
            : 'Invoice sent to printer ($printType)',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        action: SnackBarAction(
          label: isRTL ? 'عرض' : 'View',
          textColor: Colors.white,
          onPressed: () {
            _showInvoiceText(context, invoiceText, isRTL);
          },
        ),
      ),
    );
  }

  void _shareViaWhatsApp(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) async {
    final invoiceText = _generateInvoiceText(isRTL, currencyProvider);
    final encodedText = Uri.encodeComponent(invoiceText);
    final whatsappUrl = 'https://wa.me/?text=$encodedText';
    
    try {
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl));
      } else {
        // Fallback to share
        await Share.share(invoiceText);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL ? 'فشل في مشاركة الفاتورة' : 'Failed to share invoice',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareViaEmail(BuildContext context, bool isRTL, CurrencyProvider currencyProvider) async {
    final invoiceText = _generateInvoiceText(isRTL, currencyProvider);
    final subject = isRTL ? 'فاتورة رقم $invoiceId' : 'Invoice #$invoiceId';
    final emailUrl = 'mailto:?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(invoiceText)}';
    
    try {
      if (await canLaunchUrl(Uri.parse(emailUrl))) {
        await launchUrl(Uri.parse(emailUrl));
      } else {
        // Fallback to share
        await Share.share(invoiceText, subject: subject);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL ? 'فشل في إرسال الإيميل' : 'Failed to send email',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _generateInvoiceText(bool isRTL, CurrencyProvider currencyProvider) {
    final buffer = StringBuffer();
    
    if (isRTL) {
      buffer.writeln('🧾 فاتورة رقم: $invoiceId');
      buffer.writeln('📅 التاريخ: ${date.day}/${date.month}/${date.year}');
      buffer.writeln('👤 العميل: $customerName');
      if (paymentMethod != null) {
        buffer.writeln('💳 طريقة الدفع: $paymentMethod');
      }
      buffer.writeln('');
      buffer.writeln('📋 تفاصيل الفاتورة:');
      buffer.writeln('=' * 30);
      
      for (final item in items) {
        buffer.writeln(item.product.name);
        buffer.writeln('الكمية: ${item.quantity} × ${currencyProvider.formatCurrency(item.price)}');
        buffer.writeln('المجموع: ${currencyProvider.formatCurrency(item.price * item.quantity)}');
        buffer.writeln('---');
      }
      
      buffer.writeln('=' * 30);
      buffer.writeln('💰 المجموع الإجمالي: ${currencyProvider.formatCurrency(totalAmount)}');
    } else {
      buffer.writeln('🧾 Invoice #$invoiceId');
      buffer.writeln('📅 Date: ${date.day}/${date.month}/${date.year}');
      buffer.writeln('👤 Customer: $customerName');
      if (paymentMethod != null) {
        buffer.writeln('💳 Payment Method: $paymentMethod');
      }
      buffer.writeln('');
      buffer.writeln('📋 Invoice Details:');
      buffer.writeln('=' * 30);
      
      for (final item in items) {
        buffer.writeln(item.product.name);
        buffer.writeln('Qty: ${item.quantity} × ${currencyProvider.formatCurrency(item.price)}');
        buffer.writeln('Total: ${currencyProvider.formatCurrency(item.price * item.quantity)}');
        buffer.writeln('---');
      }
      
      buffer.writeln('=' * 30);
      buffer.writeln('💰 Grand Total: ${currencyProvider.formatCurrency(totalAmount)}');
    }
    
    return buffer.toString();
  }

  void _showInvoiceText(BuildContext context, String invoiceText, bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isRTL ? 'نص الفاتورة' : 'Invoice Text',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Text(
            invoiceText,
            style: GoogleFonts.robotoMono(fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isRTL ? 'إغلاق' : 'Close',
              style: GoogleFonts.cairo(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Share.share(invoiceText);
              Navigator.pop(context);
            },
            child: Text(
              isRTL ? 'مشاركة' : 'Share',
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }
}
