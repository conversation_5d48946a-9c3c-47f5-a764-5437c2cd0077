
/// مساعد للتعامل مع النصوص العربية خاصة في سياق الطباعة والعرض
/// Helper for handling Arabic text especially in printing and display context
class ArabicUtils {
  /// تصحيح ترتيب النص العربي للطباعة الحرارية
  /// معظم الطابعات الحرارية لا تدعم العربية بشكل صحيح، فنحتاج إلى معالجة خاصة
  /// Fix Arabic text ordering for thermal printing
  /// Most thermal printers don't support Arabic correctly, so we need special handling
  static String fixArabicTextForPrinting(String text) {
    if (text.isEmpty) return text;
    
    // هذه الطريقة مناسبة للطابعات التي لا تدعم Unicode بشكل صحيح
    // تعكس ترتيب الأحرف في النص مع الحفاظ على الأرقام والرموز الخاصة
    
    // تقسيم النص إلى أجزاء عربية وغير عربية
    List<String> parts = [];
    RegExp arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+');
    RegExp latinRegex = RegExp(r'[A-Za-z0-9]+');
    
    int lastEnd = 0;
    
    // العثور على كل الأجزاء العربية
    for (Match match in arabicRegex.allMatches(text)) {
      // إضافة النص قبل الجزء العربي
      if (match.start > lastEnd) {
        parts.add(text.substring(lastEnd, match.start));
      }
      
      // إضافة الجزء العربي معكوسًا
      String arabicPart = text.substring(match.start, match.end);
      parts.add(_reverseArabicString(arabicPart));
      
      lastEnd = match.end;
    }
    
    // إضافة النص المتبقي بعد آخر جزء عربي
    if (lastEnd < text.length) {
      parts.add(text.substring(lastEnd));
    }
    
    // دمج الأجزاء معًا
    return parts.join('');
  }

  /// عكس سلسلة نصية عربية مع الحفاظ على ترتيب الأرقام والرموز الخاصة
  /// Reverse an Arabic string while preserving numbers and special symbols
  static String _reverseArabicString(String text) {
    // قائمة الحروف
    List<String> chars = text.split('');
    // عكس ترتيب الحروف
    return String.fromCharCodes(chars.reversed.map((c) => c.codeUnitAt(0)));
  }
  
  /// التحقق مما إذا كان النص يحتوي على أحرف عربية
  /// Check if text contains Arabic characters
  static bool containsArabic(String text) {
    // نطاق الأحرف العربية في Unicode
    RegExp arabicChars = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]');
    return arabicChars.hasMatch(text);
  }
  
  /// الحصول على الاتجاه المناسب للنص (RTL للعربية، LTR للإنجليزية)
  /// Get the appropriate text direction (RTL for Arabic, LTR for English)
  static TextDirection getTextDirection(String text) {
    return containsArabic(text) ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// تنسيق رقم لعرضه بالعربية أو الإنجليزية حسب السياق
  /// Format a number for display in Arabic or English based on context
  static String formatNumber(num number, bool useArabicDigits) {
    if (!useArabicDigits) return number.toString();
    
    // تحويل الأرقام الإنجليزية إلى أرقام عربية
    String englishNumber = number.toString();
    String arabicNumber = englishNumber.replaceAllMapped(
      RegExp(r'[0-9]'),
      (match) {
        String digit = match.group(0)!;
        // الأرقام العربية تبدأ من U+0660 في Unicode
        int arabicDigit = int.parse(digit) + 0x0660;
        return String.fromCharCode(arabicDigit);
      }
    );
    
    return arabicNumber;
  }
}
