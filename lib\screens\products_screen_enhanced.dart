import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/screens/product_form_new.dart';
import 'package:pos_app/models/product.dart';

class ProductsScreenEnhanced extends StatefulWidget {
  const ProductsScreenEnhanced({super.key});

  @override
  State<ProductsScreenEnhanced> createState() => _ProductsScreenEnhancedState();
}

class _ProductsScreenEnhancedState extends State<ProductsScreenEnhanced> {
  bool _isGridView = true;
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _sortBy = 'name'; // name, price, stock, category
  bool _sortAscending = true;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  List<Product> get filteredAndSortedProducts {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    var products = productProvider.products;

    // Apply filters
    products =
        products.where((product) {
          final matchesSearch =
              product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              product.barcode.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              );
          final matchesCategory =
              _selectedCategory == 'All' ||
              product.category == _selectedCategory;
          return matchesSearch && matchesCategory;
        }).toList();

    // Apply sorting
    products.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'stock':
          comparison = a.stock.compareTo(b.stock);
          break;
        case 'category':
          comparison = a.category.compareTo(b.category);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return products;
  }

  int get _crossAxisCount {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4; // Desktop
    if (width > 800) return 3; // Tablet
    return 2; // Mobile
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        title: Text(
          isRTL ? 'المنتجات' : 'Products',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () => setState(() => _isGridView = !_isGridView),
            tooltip: isRTL ? 'تغيير العرض' : 'Toggle View',
          ),
          _buildSortMenu(),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilterBar(),
          _buildStatsBar(),
          Expanded(
            child: Consumer<ProductProvider>(
              builder: (context, productProvider, _) {
                if (productProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final products = filteredAndSortedProducts;

                if (products.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: productProvider.loadProducts,
                  child:
                      _isGridView
                          ? _buildGridView(products)
                          : _buildListView(products),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed:
            () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const ProductFormScreen()),
            ),
        icon: const Icon(Icons.add),
        label: Text(isRTL ? 'إضافة منتج' : 'Add Product'),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,

            decoration: InputDecoration(
              hintText: isRTL ? 'البحث في المنتجات...' : 'Search products...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                            _searchController.clear();
                          });
                        },
                      )
                      : null,
              filled: true,
              fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),
          // Category Filter
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip('All', isRTL ? 'الكل' : 'All'),
                ...Provider.of<CategoryProvider>(context).categories.map(
                  (category) =>
                      _buildCategoryChip(category.name, category.name),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String value, String label) {
    final isSelected = _selectedCategory == value;
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => setState(() => _selectedCategory = value),
        backgroundColor: theme.colorScheme.surface,
        selectedColor: theme.colorScheme.primaryContainer,
        checkmarkColor: theme.colorScheme.primary,
        labelStyle: TextStyle(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildStatsBar() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);
    final products = filteredAndSortedProducts;
    final totalProducts = products.length;
    final lowStockProducts = products.where((p) => p.stock < 10).length;
    final outOfStockProducts = products.where((p) => p.stock == 0).length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.inventory_2,
            label: isRTL ? 'إجمالي' : 'Total',
            value: totalProducts.toString(),
            color: theme.colorScheme.primary,
          ),
          _buildStatItem(
            icon: Icons.warning,
            label: isRTL ? 'مخزون قليل' : 'Low Stock',
            value: lowStockProducts.toString(),
            color: Colors.orange,
          ),
          _buildStatItem(
            icon: Icons.error,
            label: isRTL ? 'نفد المخزون' : 'Out of Stock',
            value: outOfStockProducts.toString(),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
        ),
      ],
    );
  }

  Widget _buildSortMenu() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    return PopupMenuButton<String>(
      icon: const Icon(Icons.sort),
      tooltip: isRTL ? 'ترتيب' : 'Sort',
      onSelected: (value) {
        if (value == _sortBy) {
          setState(() => _sortAscending = !_sortAscending);
        } else {
          setState(() {
            _sortBy = value;
            _sortAscending = true;
          });
        }
      },
      itemBuilder:
          (context) => [
            _buildSortMenuItem('name', isRTL ? 'الاسم' : 'Name'),
            _buildSortMenuItem('price', isRTL ? 'السعر' : 'Price'),
            _buildSortMenuItem('stock', isRTL ? 'المخزون' : 'Stock'),
            _buildSortMenuItem('category', isRTL ? 'الفئة' : 'Category'),
          ],
    );
  }

  PopupMenuItem<String> _buildSortMenuItem(String value, String label) {
    final isSelected = _sortBy == value;
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Text(label),
          const Spacer(),
          if (isSelected)
            Icon(
              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
              size: 16,
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            isRTL ? 'لا توجد منتجات' : 'No products found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'ابدأ بإضافة منتجك الأول'
                : 'Start by adding your first product',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed:
                () => Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const ProductFormScreen()),
                ),
            icon: const Icon(Icons.add),
            label: Text(isRTL ? 'إضافة منتج' : 'Add Product'),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView(List<Product> products) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _crossAxisCount,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: products.length,
      itemBuilder:
          (context, index) => EnhancedProductGridItem(
            product: products[index],
            onTap: () => _editProduct(products[index]),
            onDelete: () => _deleteProduct(products[index]),
          ),
    );
  }

  Widget _buildListView(List<Product> products) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: products.length,
      itemBuilder:
          (context, index) => EnhancedProductListItem(
            product: products[index],
            onTap: () => _editProduct(products[index]),
            onDelete: () => _deleteProduct(products[index]),
          ),
    );
  }

  void _editProduct(Product product) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => ProductFormScreen(product: product)),
    );
  }

  void _deleteProduct(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'حذف المنتج' : 'Delete Product'),
            content: Text(
              isRTL
                  ? 'هل أنت متأكد من حذف "${product.name}"؟'
                  : 'Are you sure you want to delete "${product.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Provider.of<ProductProvider>(
                    context,
                    listen: false,
                  ).deleteProduct(product.id!);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isRTL
                            ? 'تم حذف "${product.name}" بنجاح'
                            : '"${product.name}" has been deleted',
                      ),
                    ),
                  );
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: Text(isRTL ? 'حذف' : 'Delete'),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
