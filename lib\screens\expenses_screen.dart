import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/models/expense.dart';
import 'package:pos_app/providers/expense_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({super.key});

  @override
  _ExpensesScreenState createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  String _selectedCategory = 'الكل';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  final NumberFormat _currencyFormat = NumberFormat.currency(
    symbol: 'DA ',
    decimalDigits: 2,
  );

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'المصروفات' : 'Expenses',
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
            tooltip: isRTL ? 'تصفية' : 'Filter',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: isRTL ? 'بحث عن المصروفات...' : 'Search expenses...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Category Filter Chips
          Consumer<ExpenseProvider>(
            builder: (context, expenseProvider, _) {
              final categories = ['الكل', ...expenseProvider.getExpenseCategories()];
              
              return SizedBox(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    final isSelected = _selectedCategory == category;
                    
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                        backgroundColor: theme.colorScheme.surfaceContainerHighesterHighest,
                        selectedColor: theme.colorScheme.primaryContainer,
                        checkmarkColor: theme.colorScheme.primary,
                        labelStyle: TextStyle(
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurfaceVariant,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),

          // Expenses Summary Card
          Consumer<ExpenseProvider>(
            builder: (context, expenseProvider, _) {
              final totalExpenses = expenseProvider.getTotalExpenses();
              final categoryTotal = _selectedCategory == 'الكل'
                  ? totalExpenses
                  : expenseProvider.getTotalExpensesByCategory(_selectedCategory);
              
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              isRTL ? 'إجمالي المصروفات:' : 'Total Expenses:',
                              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _currencyFormat.format(totalExpenses),
                              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        if (_selectedCategory != 'الكل') ...[
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isRTL
                                    ? 'إجمالي $_selectedCategory:'
                                    : 'Total $_selectedCategory:',
                                style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _currencyFormat.format(categoryTotal),
                                style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.secondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // Expenses List
          Expanded(
            child: Consumer<ExpenseProvider>(
              builder: (context, expenseProvider, _) {
                if (expenseProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final allExpenses = expenseProvider.expenses;
                final filteredExpenses = allExpenses.where((expense) {
                  final matchesSearch = _searchQuery.isEmpty ||
                      expense.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                      expense.description?.toLowerCase().contains(_searchQuery.toLowerCase()) == true;
                  
                  final matchesCategory = _selectedCategory == 'الكل' ||
                      expense.category == _selectedCategory;
                  
                  return matchesSearch && matchesCategory;
                }).toList();

                if (filteredExpenses.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long,
                          size: 64,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          isRTL ? 'لا توجد مصروفات' : 'No expenses found',
                          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredExpenses.length,
                  itemBuilder: (context, index) {
                    final expense = filteredExpenses[index];
                    return _buildExpenseCard(context, expense);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddExpenseDialog(context),
        tooltip: 'Add New Expense',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildExpenseCard(BuildContext context, Expense expense) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primaryContainer,
          child: Icon(
            _getCategoryIcon(expense.category),
            color: theme.colorScheme.primary,
          ),
        ),
        title: Text(
          expense.title,
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  _dateFormat.format(expense.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 12),
                Icon(
                  Icons.category,
                  size: 14,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  expense.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            if (expense.description != null && expense.description!.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                expense.description!,
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _currencyFormat.format(expense.amount),
              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.edit,
                    size: 18,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () => _showEditExpenseDialog(context, expense),
                  constraints: const BoxConstraints(),
                  padding: const EdgeInsets.all(4),
                ),
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    size: 18,
                    color: theme.colorScheme.error,
                  ),
                  onPressed: () => _showDeleteExpenseDialog(context, expense),
                  constraints: const BoxConstraints(),
                  padding: const EdgeInsets.all(4),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _showExpenseDetails(context, expense),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'إيجار':
      case 'rent':
        return FontAwesomeIcons.building;
      case 'مرافق':
      case 'utilities':
        return FontAwesomeIcons.bolt;
      case 'رواتب':
      case 'salaries':
        return FontAwesomeIcons.moneyBill;
      case 'صيانة':
      case 'maintenance':
        return FontAwesomeIcons.screwdriverWrench;
      case 'تسويق':
      case 'marketing':
        return FontAwesomeIcons.bullhorn;
      case 'مخزون':
      case 'inventory':
        return FontAwesomeIcons.boxesStacked;
      case 'نقل':
      case 'transportation':
        return FontAwesomeIcons.truck;
      case 'أخرى':
      case 'other':
        return FontAwesomeIcons.receipt;
      default:
        return FontAwesomeIcons.fileInvoiceDollar;
    }
  }

  void _showFilterDialog(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'تصفية المصروفات' : 'Filter Expenses'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Filter options can be added here
            Text(
              isRTL ? 'قريباً: المزيد من خيارات التصفية' : 'Coming soon: More filter options',
              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  void _showAddExpenseDialog(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    final titleController = TextEditingController();
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedCategory = 'أخرى';
    DateTime selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'إضافة مصروف جديد' : 'Add New Expense'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: isRTL ? 'العنوان' : 'Title',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                decoration: InputDecoration(
                  labelText: isRTL ? 'المبلغ' : 'Amount',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              Consumer<ExpenseProvider>(
                builder: (context, expenseProvider, _) {
                  final categories = {
                    'إيجار',
                    'مرافق',
                    'رواتب',
                    'صيانة',
                    'تسويق',
                    'مخزون',
                    'نقل',
                    'أخرى',
                    ...expenseProvider.getExpenseCategories()
                  });
                  
                  return DropdownButtonFormField<String>(
                    value: selectedCategory,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'الفئة' : 'Category',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedCategory = value;
                      }
                    },
                  );
                },
              ),
              const SizedBox(height = 16),
              InkWell(
                onTap = () async {
                  final pickedDate = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (pickedDate != null) {
                    selectedDate = pickedDate;
                  }
                },
                child = InputDecorator(
                  decoration: InputDecoration(
                    labelText: isRTL ? 'التاريخ' : 'Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  child: Text(_dateFormat.format(selectedDate)),
                ),
              ),
              const SizedBox(height = 16),
              TextField(
                controller = descriptionController,
                decoration = InputDecoration(
                  labelText: isRTL ? 'الوصف (اختياري)' : 'Description (Optional)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines = 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed = () => Navigator.of(ctx).pop(),
            child = Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed = () {
              if (titleController.text.isNotEmpty && amountController.text.isNotEmpty) {
                final expense = Expense(
                  title: titleController.text,
                  amount: double.tryParse(amountController.text) ?? 0,
                  category: selectedCategory,
                  date: selectedDate,
                  description: descriptionController.text.isEmpty
                      ? null
                      : descriptionController.text,
                );
                
                Provider.of<ExpenseProvider>(context, listen: false)
                    .addExpense(expense);
                
                Navigator.of(ctx).pop();
              }
            },
            child = Text(isRTL ? 'إضافة' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showEditExpenseDialog(BuildContext context, Expense expense) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    final titleController = TextEditingController(text: expense.title);
    final amountController = TextEditingController(text: expense.amount.toString());
    final descriptionController = TextEditingController(text: expense.description ?? '');
    String selectedCategory = expense.category;
    DateTime selectedDate = expense.date;

    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'تعديل المصروف' : 'Edit Expense'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: isRTL ? 'العنوان' : 'Title',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                decoration: InputDecoration(
                  labelText: isRTL ? 'المبلغ' : 'Amount',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              Consumer<ExpenseProvider>(
                builder: (context, expenseProvider, _) {
                  final categories = {
                    'إيجار',
                    'مرافق',
                    'رواتب',
                    'صيانة',
                    'تسويق',
                    'مخزون',
                    'نقل',
                    'أخرى',
                    ...expenseProvider.getExpenseCategories()
                  });
                  
                  return DropdownButtonFormField<String>(
                    value: selectedCategory,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'الفئة' : 'Category',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        selectedCategory = value;
                      }
                    },
                  );
                },
              ),
              SizedBox(height = 16),
              InkWell(
                onTap = () async {
                  final pickedDate = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (pickedDate != null) {
                    selectedDate = pickedDate;
                  }
                },
                child = InputDecorator(
                  decoration: InputDecoration(
                    labelText: isRTL ? 'التاريخ' : 'Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  child: Text(_dateFormat.format(selectedDate)),
                ),
              ),
              SizedBox(height = 16),
              TextField(
                controller = descriptionController,
                decoration = InputDecoration(
                  labelText: isRTL ? 'الوصف (اختياري)' : 'Description (Optional)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines = 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed = () => Navigator.of(ctx).pop(),
            child = Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed = () {
              if (titleController.text.isNotEmpty && amountController.text.isNotEmpty) {
                final updatedExpense = expense.copyWith(
                  title: titleController.text,
                  amount: double.tryParse(amountController.text) ?? 0,
                  category: selectedCategory,
                  date: selectedDate,
                  description: descriptionController.text.isEmpty
                      ? null
                      : descriptionController.text,
                );
                
                Provider.of<ExpenseProvider>(context, listen: false)
                    .updateExpense(updatedExpense);
                
                Navigator.of(ctx).pop();
              }
            },
            child = Text(isRTL ? 'حفظ' : 'Save'),
          ),
        ],
      ),
    );
  }

  void _showDeleteExpenseDialog(BuildContext context, Expense expense) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'حذف المصروف' : 'Delete Expense'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من رغبتك في حذف هذا المصروف؟'
              : 'Are you sure you want to delete this expense?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<ExpenseProvider>(context, listen: false)
                  .deleteExpense(expense.id!);
              Navigator.of(ctx).pop();
            },
            child: Text(
              isRTL ? 'حذف' : 'Delete',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showExpenseDetails(BuildContext context, Expense expense) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          expense.title,
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              leading: Icon(
                Icons.attach_money,
                color: theme.colorScheme.primary,
              ),
              title: Text(isRTL ? 'المبلغ' : 'Amount'),
              subtitle: Text(
                _currencyFormat.format(expense.amount),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: Icon(
                Icons.category,
                color: theme.colorScheme.primary,
              ),
              title: Text(isRTL ? 'الفئة' : 'Category'),
              subtitle: Text(expense.category),
            ),
            ListTile(
              leading: Icon(
                Icons.calendar_today,
                color: theme.colorScheme.primary,
              ),
              title: Text(isRTL ? 'التاريخ' : 'Date'),
              subtitle: Text(_dateFormat.format(expense.date)),
            ),
            if (expense.description != null && expense.description!.isNotEmpty)
              ListTile(
                leading: Icon(
                  Icons.description,
                  color: theme.colorScheme.primary,
                ),
                title: Text(isRTL ? 'الوصف' : 'Description'),
                subtitle: Text(expense.description!),
              ),
            if (expense.receiptImagePath != null)
              ListTile(
                leading: Icon(
                  Icons.receipt,
                  color: theme.colorScheme.primary,
                ),
                title: Text(isRTL ? 'الإيصال' : 'Receipt'),
                subtitle: Text(isRTL ? 'اضغط لعرض الإيصال' : 'Tap to view receipt'),
                onTap: () {
                  // Show receipt image
                },
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              _showEditExpenseDialog(context, expense);
            },
            child: Text(isRTL ? 'تعديل' : 'Edit'),
          ),
        ],
      ),
    );
  }
}
