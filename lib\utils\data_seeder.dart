import 'package:flutter/foundation.dart' show debugPrint;
import 'package:pos_app/db/database_helper.dart';
import 'package:pos_app/models/product.dart';
class DataSeeder {
  static Future<void> seedData() async {
    try {
      debugPrint('Starting data seeding...');
      await seedCategories();
      await seedProducts();
      await seedCustomers();
      await seedSuppliers();
      debugPrint('Data seeding completed successfully');
    } catch (e) {
      debugPrint('Error seeding data: $e');
    }
  }

  static Future<void> seedCategories() async {
    try {
      final db = DatabaseHelper.instance;
      final categories = await db.getAllCategories();
      
      if (categories.isEmpty) {
        debugPrint('Seeding categories...');
        final defaultCategories = [
          Category(name: 'Beverages', iconName: 'local_cafe'),
          Category(name: 'Bakery', iconName: 'bakery_dining'),
          Category(name: 'Snacks', iconName: 'lunch_dining'),
          Category(name: 'Food', iconName: 'restaurant'),
          Category(name: 'Dairy', iconName: 'egg'),
          Category(name: 'Produce', iconName: 'eco'),
        ];
        
        for (var category in defaultCategories) {
          await db.insertCategory(category);
        }
        debugPrint('${defaultCategories.length} categories seeded');
      } else {
        debugPrint('Categories already exist, skipping seed');
      }
    } catch (e) {
      debugPrint('Error seeding categories: $e');
    }
  }

  static Future<void> seedProducts() async {
    try {
      final db = DatabaseHelper.instance;
      final products = await db.getAllProducts();
    
    if (products.isEmpty) {
      await db.insertProduct(
      Product(
        name: 'Coffee',
        barcode: '1234567890123',
        price: 3.99,
        costPrice: 2.25,
        wholesalePrice: 2.99,
        enableWholesale: true,
        minWholesaleQty: 10,
        stock: 100,
        category: 'Beverages',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Green Tea',
        barcode: '1234567890124',
        price: 2.99,
        costPrice: 1.75,
        wholesalePrice: 2.25,
        enableWholesale: true,
        minWholesaleQty: 10,
        stock: 80,
        category: 'Beverages',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Chocolate Muffin',
        barcode: '1234567890125',
        price: 2.49,
        costPrice: 1.20,
        wholesalePrice: 1.85,
        enableWholesale: true,
        minWholesaleQty: 20,
        stock: 50,
        category: 'Bakery',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Sandwich',
        barcode: '1234567890126',
        price: 5.99,
        costPrice: 3.50,
        wholesalePrice: 4.75,
        enableWholesale: true,
        minWholesaleQty: 10,
        stock: 30,
        category: 'Food',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Bottled Water',
        barcode: '1234567890127',
        price: 1.49,
        costPrice: 0.65,
        wholesalePrice: 0.99,
        enableWholesale: true,
        minWholesaleQty: 24,
        stock: 200,
        category: 'Beverages',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Chocolate Bar',
        barcode: '1234567890128',
        price: 1.99,
        costPrice: 1.10,
        wholesalePrice: 1.50,
        enableWholesale: true,
        minWholesaleQty: 20,
        stock: 75,
        category: 'Snacks',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Potato Chips',
        barcode: '1234567890129',
        price: 2.29,
        costPrice: 1.15,
        wholesalePrice: 1.75,
        enableWholesale: true,
        minWholesaleQty: 15,
        stock: 60,
        category: 'Snacks',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Orange Juice',
        barcode: '1234567890130',
        price: 3.49,
        costPrice: 2.10,
        wholesalePrice: 2.75,
        enableWholesale: true,
        minWholesaleQty: 10,
        stock: 40,
        category: 'Beverages',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Bagel',
        barcode: '1234567890131',
        price: 1.79,
        costPrice: 0.85,
        wholesalePrice: 1.25,
        enableWholesale: true,
        minWholesaleQty: 12,
        stock: 45,
        category: 'Bakery',
      ),
      );
      await db.insertProduct(
      Product(
        name: 'Salad',
        barcode: '1234567890132',
        price: 6.99,
        costPrice: 4.25,
        wholesalePrice: 5.50,
        enableWholesale: false,
        minWholesaleQty: 10,
        stock: 25,
        category: 'Food',
      ),
      );
    }
    } catch (e) {
      debugPrint('Error seeding suppliers: $e');
    }
  }

  static Future<void> seedCustomers() async {
    try {
      final db = await DatabaseHelper.instance.database;
      final List<Map<String, dynamic>> customerCount = await db.query('customers');
    
    if (customerCount.isEmpty) {
      await db.insert('customers', {
        'name': 'Ahmed Hassan',
        'phone': '0555123456',
        'email': '<EMAIL>',
        'address': 'Algiers, Algeria',
        'balance': 1500.0,
        'notes': 'Regular customer'
      });
      
      await db.insert('customers', {
        'name': 'Fatima Benali',
        'phone': '0666987654',
        'email': '<EMAIL>',
        'address': 'Oran, Algeria',
        'balance': 750.0,
        'notes': 'Wholesale buyer'
      });
      
      await db.insert('customers', {
        'name': 'Mohammed Kaci',
        'phone': '0777456789',
        'email': '<EMAIL>',
        'address': 'Constantine, Algeria',
        'balance': 2200.0,
        'notes': 'Business client'
      });
    }
    } catch (e) {
      debugPrint('Error seeding customers: $e');
    }
  }

  static Future<void> seedSuppliers() async {
    try {
      final db = await DatabaseHelper.instance.database;
      final List<Map<String, dynamic>> supplierCount = await db.query('suppliers');
    
    if (supplierCount.isEmpty) {
      await db.insert('suppliers', {
        'name': 'Algerian Foods Ltd',
        'phone': '021123456',
        'email': '<EMAIL>',
        'address': 'Zone Industrielle, Algiers',
        'balance': 12500.0,
        'notes': 'Main supplier for local products'
      });
      
      await db.insert('suppliers', {
        'name': 'Tech Imports LLC',
        'phone': '021987654',
        'email': '<EMAIL>',
        'address': 'Industrial Park, Oran',
        'balance': 8750.0,
        'notes': 'Electronics and appliances supplier'
      });
      
      await db.insert('suppliers', {
        'name': 'Global Distribution Co.',
        'phone': '021456789',
        'email': '<EMAIL>',
        'address': 'Commercial Hub, Constantine',
        'balance': 15200.0,
        'notes': 'International imported goods'
      });
    }
    } catch (e) {
      debugPrint('Error seeding suppliers: $e');
    }
  }
}
