import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pos_app/models/category.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

/// بطاقة الفئة المحسنة مع دعم اللغة العربية
class CategoryCard extends StatelessWidget {
  final Category category;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CategoryCard({
    super.key,
    super.red this.category,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    IconData iconData = Icons.category;
    
    if (category.iconName != null) {
      switch (category.iconName) {
        case 'local_cafe': iconData = Icons.local_cafe; break;
        case 'bakery_dining': iconData = Icons.bakery_dining; break;
        case 'lunch_dining': iconData = Icons.lunch_dining; break;
        case 'restaurant': iconData = Icons.restaurant; break;
        case 'egg': iconData = Icons.egg_alt; break;
        case 'eco': iconData = Icons.eco; break;
        case 'shopping_bag': iconData = Icons.shopping_bag; break;
        case 'checkroom': iconData = Icons.checkroom; break;
        case 'devices': iconData = Icons.devices; break;
        case 'book': iconData = Icons.book; break;
        case 'sports_soccer': iconData = Icons.sports_soccer; break;
      }
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withOpacity(0.7),
                shape: BoxShape.circle,
              ),
              child: Icon(
                iconData,
                size: 36,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              category.name,
              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              isRTL 
                ? '${category.productCount ?? 0} منتج' 
                : '${category.productCount ?? 0} Products',
              style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                fontSize: 12,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, size: 18),
                  onPressed: onEdit,
                  color: theme.colorScheme.primary,
                  tooltip: isRTL ? 'تعديل' : 'Edit',
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 18),
                  onPressed: onDelete,
                  color: theme.colorScheme.error,
                  tooltip: isRTL ? 'حذف' : 'Delete',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// منتقي الأيقونات المحسن
class IconSelector extends StatefulWidget {
  final Function(String) onIconSelected;
  final String? initialSelection;

  const IconSelector({
    super.key,
    super.red this.onIconSelected,
    this.initialSelection,
  });

  @override
  State<IconSelector> createState() => _IconSelectorState();
}

class _IconSelectorState extends State<IconSelector> {
  late String selectedIcon;

  @override
  void initState() {
    super.initState();
    selectedIcon = widget.initialSelection ?? 'category';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    
    final icons = {
      'category': Icons.category,
      'local_cafe': Icons.local_cafe,
      'bakery_dining': Icons.bakery_dining,
      'lunch_dining': Icons.lunch_dining,
      'restaurant': Icons.restaurant,
      'egg': Icons.egg_alt,
      'eco': Icons.eco,
      'shopping_bag': Icons.shopping_bag,
      'checkroom': Icons.checkroom,
      'devices': Icons.devices,
      'book': Icons.book,
      'sports_soccer': Icons.sports_soccer,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isRTL ? 'اختر أيقونة:' : 'Select Icon:',
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 70,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
            borderRadius: BorderRadiusurfaceContainerHighest,
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            itemCount: icons.length,
            itemBuilder: (context, index) {
              final entry = icons.entries.elementAt(index);
              final isSelected = selectedIcon == entry.key;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedIcon = entry.key;
                  });
                  widget.onIconSelected(entry.key);
                },
                child: Container(
                  width: 54,
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? theme.colorScheme.primaryContainer 
                        : theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.outline.withOpacity(0.5),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Icon(
                    entry.value,
                    color: isSelected 
                        ? theme.colorScheme.primary 
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// نموذج إضافة/تعديل الفئة
class CategoryForm extends StatefulWidget {
  final Category? category;
  final Function(Category) onSave;
  final VoidCallback onCancel;
  final bool isDialog;

  const CategoryForm({
    super.key,
    super.category,
    required this.onSave,
    required this.onCancel,
    this.isDialog = false,
  });

  @override
  State<CategoryForm> createState() => _CategoryFormState();
}

class _CategoryFormState extends State<CategoryForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedIcon = 'category';

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category?.description ?? '';
      _selectedIcon = widget.category?.iconName ?? 'category';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;

    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (!widget.isDialog) ...[
            Row(
              children: [
                Icon(Icons.category, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  widget.category == null
                      ? (isRTL ? 'إضافة فئة جديدة' : 'Add New Category')
                      : (isRTL ? 'تعديل الفئة' : 'Edit Category'),
                  style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
          
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: isRTL ? 'اسم الفئة' : 'Category Name',
              hintText: isRTL ? 'أدخل اسم الفئة' : 'Enter category name',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.label),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return isRTL ? 'الرجاء إدخال اسم الفئة' : 'Please enter a category name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _descriptionController,
            decoration: InputDecoration(
              labelText: isRTL ? 'الوصف (اختياري)' : 'Description (Optional)',
              hintText: isRTL ? 'أدخل وصفًا للفئة' : 'Enter category description',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.description),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          
          IconSelector(
            initialSelection: _selectedIcon,
            onIconSelected: (icon) {
              setState(() {
                _selectedIcon = icon;
              });
            },
          ),
          const SizedBox(height: 24),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: widget.onCancel,
                child: Text(
                  isRTL ? 'إلغاء' : 'Cancel',
                  style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    final category = Category(
                      id: widget.category?.id,
                      name: _nameController.text.trim(),
                      description: _descriptionController.text.trim().isEmpty 
                          ? null 
                          : _descriptionController.text.trim(),
                      iconName: _selectedIcon,
                      productCount: widget.category?.productCount,
                    );
                    widget.onSave(category);
                  }
                },
                icon: Icon(widget.category == null ? Icons.add : Icons.save),
                label: Text(
                  widget.category == null
                      ? (isRTL ? 'إضافة' : 'Add')
                      : (isRTL ? 'حفظ' : 'Save'),
                  style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
