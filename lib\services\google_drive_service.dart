import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:url_launcher/url_launcher.dart';
class GoogleDriveService {
  static const List<String> _scopes = [
    drive.DriveApi.driveFileScope,
  ];

  GoogleSignIn? _googleSignIn;
  drive.DriveApi? _driveApi;
  bool _isInitialized = false;

  GoogleDriveService() {
    _initialize();
  }

  void _initialize() {
    if (!kIsWeb) {
      _googleSignIn = GoogleSignIn(
        scopes: _scopes,
      );
    }
    _isInitialized = true;
  }

  bool get isInitialized => _isInitialized;
  bool get isSignedIn => _googleSignIn?.currentUser != null;

  Future<bool> signIn() async {
    try {
      if (kIsWeb) {
        // For web, we'll use a different approach
        return await _signInWeb();
      }

      if (_googleSignIn == null) return false;

      final GoogleSignInAccount? account = await _googleSignIn!.signIn();
      if (account == null) return false;

      final authClient = await _googleSignIn!.authenticatedClient();
      if (authClient == null) return false;

      _driveApi = drive.DriveApi(authClient);
      return true;
    } catch (e) {
      debugPrint('Google Sign-In error: $e');
      return false;
    }
  }

  Future<bool> _signInWeb() async {
    try {
      // For web, we'll open Google Drive in a new tab
      // This is a simplified approach for the demo
      const url = 'https://drive.google.com';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Web Google Drive launch error: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      if (!kIsWeb && _googleSignIn != null) {
        await _googleSignIn!.signOut();
      }
      _driveApi = null;
    } catch (e) {
      debugPrint('Google Sign-Out error: $e');
    }
  }

  Future<String?> uploadDatabaseBackup({
    required String filePath,
    required String fileName,
    String? description,
    Function(double)? onProgress,
  }) async {
    try {
      if (kIsWeb) {
        return await _uploadBackupWeb(fileName, filePath, description, onProgress);
      }

      if (_driveApi == null) {
        throw Exception('Google Drive API not initialized. Please sign in first.');
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Backup file not found: $filePath');
      }

      onProgress?.call(0.1);

      // Create file metadata
      final driveFile = drive.File()
        ..name = fileName
        ..description = description ?? 'POS App Database Backup'
        ..parents = await _getOrCreateBackupFolder();

      onProgress?.call(0.3);

      // Read file content
      final fileContent = await file.readAsBytes();
      final media = drive.Media(Stream.value(fileContent), fileContent.length);

      onProgress?.call(0.5);

      // Upload file
      final uploadedFile = await _driveApi!.files.create(
        driveFile,
        uploadMedia: media,
      );

      onProgress?.call(0.9);

      if (uploadedFile.id != null) {
        onProgress?.call(1.0);
        return uploadedFile.id;
      } else {
        throw Exception('Failed to upload file to Google Drive');
      }
    } catch (e) {
      debugPrint('Google Drive upload error: $e');
      rethrow;
    }
  }

  Future<String?> _uploadBackupWeb(
    String fileName,
    String fileContent,
    String? description,
    Function(double)? onProgress,
  ) async {
    try {
      onProgress?.call(0.2);

      // For web, we'll create a download link and open Google Drive
      final bytes = utf8.encode(fileContent);
      final blob = base64Encode(bytes);

      onProgress?.call(0.5);

      // Create a data URL for download
      final dataUrl = 'data:application/json;base64,$blob';
      
      // Create a temporary download link
      if (kIsWeb) {
        // Use web-specific download approach
        await _downloadFileWeb(dataUrl, fileName);
      }

      onProgress?.call(0.8);

      // Open Google Drive for manual upload
      const driveUrl = 'https://drive.google.com/drive/my-drive';
      if (await canLaunchUrl(Uri.parse(driveUrl))) {
        await launchUrl(Uri.parse(driveUrl), mode: LaunchMode.externalApplication);
      }

      onProgress?.call(1.0);
      return 'web_upload_$fileName';
    } catch (e) {
      debugPrint('Web upload error: $e');
      rethrow;
    }
  }

  Future<void> _downloadFileWeb(String dataUrl, String fileName) async {
    // This would require web-specific implementation
    // For now, we'll just log the action
    debugPrint('Web download initiated for: $fileName');
  }

  Future<List<String>> _getOrCreateBackupFolder() async {
    try {
      if (_driveApi == null) return [];

      // Search for existing backup folder
      final query = "name='POS_App_Backups' and mimeType='application/vnd.google-apps.folder'";
      final fileList = await _driveApi!.files.list(q: query);

      if (fileList.files != null && fileList.files!.isNotEmpty) {
        return [fileList.files!.first.id!];
      }

      // Create backup folder if it doesn't exist
      final folder = drive.File()
        ..name = 'POS_App_Backups'
        ..mimeType = 'application/vnd.google-apps.folder';

      final createdFolder = await _driveApi!.files.create(folder);
      return createdFolder.id != null ? [createdFolder.id!] : [];
    } catch (e) {
      debugPrint('Error managing backup folder: $e');
      return [];
    }
  }

  Future<List<DriveBackupFile>> listBackupFiles() async {
    try {
      if (kIsWeb) {
        // For web, return empty list as we can't directly access Drive API
        return [];
      }

      if (_driveApi == null) {
        throw Exception('Google Drive API not initialized. Please sign in first.');
      }

      final backupFolders = await _getOrCreateBackupFolder();
      if (backupFolders.isEmpty) return [];

      final query = "'${backupFolders.first}' in parents and trashed=false";
      final fileList = await _driveApi!.files.list(
        q: query,
        orderBy: 'createdTime desc',
        pageSize: 50,
      );

      if (fileList.files == null) return [];

      return fileList.files!.map((file) => DriveBackupFile(
        id: file.id ?? '',
        name: file.name ?? '',
        size: file.size != null ? int.parse(file.size!) : 0,
        createdTime: file.createdTime ?? DateTime.now(),
        description: file.description ?? '',
      )).toList();
    } catch (e) {
      debugPrint('Error listing backup files: $e');
      return [];
    }
  }

  Future<bool> downloadBackup({
    required String fileId,
    required String localPath,
    Function(double)? onProgress,
  }) async {
    try {
      if (kIsWeb) {
        throw Exception('Download not supported on web platform');
      }

      if (_driveApi == null) {
        throw Exception('Google Drive API not initialized. Please sign in first.');
      }

      onProgress?.call(0.1);

      final media = await _driveApi!.files.get(
        fileId,
        downloadOptions: drive.DownloadOptions.fullMedia,
      ) as drive.Media;

      onProgress?.call(0.3);

      final file = File(localPath);
      final sink = file.openWrite();

      await for (final chunk in media.stream) {
        sink.add(chunk);
        onProgress?.call(0.5); // Simplified progress
      }

      await sink.close();
      onProgress?.call(1.0);

      return await file.exists();
    } catch (e) {
      debugPrint('Error downloading backup: $e');
      return false;
    }
  }

  Future<bool> deleteBackup(String fileId) async {
    try {
      if (kIsWeb) {
        throw Exception('Delete not supported on web platform');
      }

      if (_driveApi == null) {
        throw Exception('Google Drive API not initialized. Please sign in first.');
      }

      await _driveApi!.files.delete(fileId);
      return true;
    } catch (e) {
      debugPrint('Error deleting backup: $e');
      return false;
    }
  }

  String get currentUserEmail {
    if (kIsWeb) return '<EMAIL>';
    return _googleSignIn?.currentUser?.email ?? '';
  }

  String get currentUserName {
    if (kIsWeb) return 'Web User';
    return _googleSignIn?.currentUser?.displayName ?? '';
  }
}

class DriveBackupFile {
  final String id;
  final String name;
  final int size;
  final DateTime createdTime;
  final String description;

  DriveBackupFile({
    required this.id,
    required this.name,
    required this.size,
    required this.createdTime,
    required this.description,
  });

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String get formattedDate {
    return '${createdTime.day}/${createdTime.month}/${createdTime.year} ${createdTime.hour}:${createdTime.minute.toString().padLeft(2, '0')}';
  }
}
