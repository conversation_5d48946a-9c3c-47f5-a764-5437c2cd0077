import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/customer_provider.dart';
import 'package:pos_app/providers/invoice_provider.dart';
import 'package:pos_app/services/pdf_export_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<
      LocaleProvider,
      ProductProvider,
      CustomerProvider,
      InvoiceProvider
    >(
      builder: (
        context,
        localeProvider,
        productProvider,
        customerProvider,
        invoiceProvider,
        _,
      ) {
        final isRTL = localeProvider.isRTL;

        // Load data if not already loaded
        if (!customerProvider.isLoading && customerProvider.customers.isEmpty) {
          Future.microtask(() => customerProvider.loadCustomers());
        }
        if (!invoiceProvider.isLoading && invoiceProvider.invoices.isEmpty) {
          Future.microtask(() => invoiceProvider.loadInvoices());
        }
        if (!productProvider.isLoading && productProvider.products.isEmpty) {
          Future.microtask(() => productProvider.loadProducts());
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              isRTL ? 'التقارير' : 'Reports',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            actions: [
              // Date Range Selector
              IconButton(
                icon: const FaIcon(FontAwesomeIcons.calendar, size: 20),
                tooltip: isRTL ? 'اختيار الفترة' : 'Select Date Range',
                onPressed: () => _selectDateRange(context, isRTL),
              ),
              // Export PDF Button
              IconButton(
                icon: const FaIcon(FontAwesomeIcons.filePdf, size: 20),
                tooltip: isRTL ? 'تصدير PDF' : 'Export PDF',
                onPressed:
                    _isLoading
                        ? null
                        : () => _exportCurrentReport(context, isRTL),
              ),
            ],
          ),
          body: Column(
            children: [
              // Date Range Display
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF4CAF50),
                      const Color(0xFF4CAF50).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    FaIcon(
                      FontAwesomeIcons.calendar,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        isRTL
                            ? 'من ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}'
                            : 'From ${DateFormat('dd/MM/yyyy').format(_startDate)} to ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (_isLoading)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Tab Bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: const Color(0xFF4CAF50),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  tabs: [
                    Tab(
                      text: isRTL ? 'المبيعات' : 'Sales',
                      icon: const FaIcon(FontAwesomeIcons.chartLine, size: 16),
                    ),
                    Tab(
                      text: isRTL ? 'المخزون' : 'Inventory',
                      icon: const FaIcon(
                        FontAwesomeIcons.boxesStacked,
                        size: 16,
                      ),
                    ),
                    Tab(
                      text: isRTL ? 'العملاء' : 'Customers',
                      icon: const FaIcon(FontAwesomeIcons.users, size: 16),
                    ),
                    Tab(
                      text: isRTL ? 'الأرباح' : 'Profits',
                      icon: const FaIcon(FontAwesomeIcons.chartPie, size: 16),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Tab Views
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSalesReport(invoiceProvider, isRTL),
                    _buildInventoryReport(productProvider, isRTL),
                    _buildCustomersReport(customerProvider, isRTL),
                    _buildProfitsReport(invoiceProvider, isRTL),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectDateRange(BuildContext context, bool isRTL) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFF4CAF50)),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  Future<void> _exportCurrentReport(BuildContext context, bool isRTL) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentTab = _tabController.index;
      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final invoiceProvider = Provider.of<InvoiceProvider>(
        context,
        listen: false,
      );

      late String fileName;
      late String title;

      switch (currentTab) {
        case 0: // Sales Report
          title = isRTL ? 'تقرير المبيعات' : 'Sales Report';
          fileName =
              'sales_report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}';
          final pdfBytes = await PDFExportService.generateSalesReport(
            invoices: invoiceProvider.invoices,
            title: title,
            startDate: _startDate,
            endDate: _endDate,
            isRTL: isRTL,
            companyName: isRTL ? 'نظام نقاط البيع' : 'POS System',
          );
          await PDFExportService.shareReport(pdfBytes, fileName);
          break;

        case 1: // Inventory Report
          title = isRTL ? 'تقرير المخزون' : 'Inventory Report';
          fileName =
              'inventory_report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}';
          final pdfBytes = await PDFExportService.generateInventoryReport(
            products: productProvider.products,
            title: title,
            dateRange: DateTime.now(),
            isRTL: isRTL,
            companyName: isRTL ? 'نظام نقاط البيع' : 'POS System',
          );
          await PDFExportService.shareReport(pdfBytes, fileName);
          break;

        case 2: // Customers Report
          title = isRTL ? 'تقرير العملاء' : 'Customers Report';
          fileName =
              'customers_report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}';
          final pdfBytes = await PDFExportService.generateCustomerReport(
            customers: customerProvider.customers,
            title: title,
            dateRange: DateTime.now(),
            isRTL: isRTL,
            companyName: isRTL ? 'نظام نقاط البيع' : 'POS System',
          );
          await PDFExportService.shareReport(pdfBytes, fileName);
          break;

        case 3: // Profits Report
          title = isRTL ? 'تقرير الأرباح' : 'Profits Report';
          fileName =
              'profits_report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}';
          final pdfBytes = await PDFExportService.generateSalesReport(
            invoices: invoiceProvider.invoices,
            title: title,
            startDate: _startDate,
            endDate: _endDate,
            isRTL: isRTL,
            companyName: isRTL ? 'نظام نقاط البيع' : 'POS System',
          );
          await PDFExportService.shareReport(pdfBytes, fileName);
          break;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم تصدير التقرير بنجاح' : 'Report exported successfully',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFF4CAF50),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'فشل في تصدير التقرير' : 'Failed to export report',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildSalesReport(InvoiceProvider invoiceProvider, bool isRTL) {
    final filteredInvoices =
        invoiceProvider.invoices.where((invoice) {
          return invoice.date.isAfter(
                _startDate.subtract(const Duration(days: 1)),
              ) &&
              invoice.date.isBefore(_endDate.add(const Duration(days: 1)));
        }).toList();

    final totalSales = filteredInvoices.fold<double>(
      0,
      (sum, invoice) => sum + invoice.finalAmount,
    );
    final totalInvoices = filteredInvoices.length;
    final averageSale = totalInvoices > 0 ? totalSales / totalInvoices : 0.0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: isRTL ? 'إجمالي المبيعات' : 'Total Sales',
                  value: totalSales.toStringAsFixed(2),
                  icon: FontAwesomeIcons.dollarSign,
                  color: const Color(0xFF4CAF50),
                  isRTL: isRTL,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: isRTL ? 'عدد الفواتير' : 'Total Invoices',
                  value: totalInvoices.toString(),
                  icon: FontAwesomeIcons.receipt,
                  color: Colors.blue,
                  isRTL: isRTL,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryCard(
            title: isRTL ? 'متوسط البيع' : 'Average Sale',
            value: averageSale.toStringAsFixed(2),
            icon: FontAwesomeIcons.chartLine,
            color: Colors.orange,
            isRTL: isRTL,
          ),

          const SizedBox(height: 24),

          // Recent Invoices
          Text(
            isRTL ? 'الفواتير الحديثة' : 'Recent Invoices',
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          ...filteredInvoices
              .take(10)
              .map(
                (invoice) => Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: const Color(
                        0xFF4CAF50,
                      ).withValues(alpha: 0.1),
                      child: FaIcon(
                        FontAwesomeIcons.receipt,
                        color: const Color(0xFF4CAF50),
                        size: 16,
                      ),
                    ),
                    title: Text(
                      invoice.invoiceNumber,
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                    subtitle: Text(
                      '${invoice.customerName} • ${DateFormat('dd/MM/yyyy').format(invoice.date)}',
                      style: GoogleFonts.cairo(fontSize: 12),
                    ),
                    trailing: Text(
                      invoice.finalAmount.toStringAsFixed(2),
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4CAF50),
                      ),
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildInventoryReport(ProductProvider productProvider, bool isRTL) {
    final products = productProvider.products;
    final totalProducts = products.length;
    final totalValue = products.fold<double>(
      0,
      (sum, product) => sum + (product.price * product.stock),
    );
    final lowStockCount = products.where((p) => p.stock < 10).length;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: isRTL ? 'إجمالي المنتجات' : 'Total Products',
                  value: totalProducts.toString(),
                  icon: FontAwesomeIcons.boxesStacked,
                  color: const Color(0xFF4CAF50),
                  isRTL: isRTL,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: isRTL ? 'قيمة المخزون' : 'Inventory Value',
                  value: totalValue.toStringAsFixed(2),
                  icon: FontAwesomeIcons.dollarSign,
                  color: Colors.blue,
                  isRTL: isRTL,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryCard(
            title: isRTL ? 'مخزون قليل' : 'Low Stock Items',
            value: lowStockCount.toString(),
            icon: FontAwesomeIcons.triangleExclamation,
            color: Colors.orange,
            isRTL: isRTL,
          ),

          const SizedBox(height: 24),

          // Low Stock Alert
          if (lowStockCount > 0) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.triangleExclamation,
                        color: Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isRTL
                            ? 'تنبيه: منتجات بمخزون قليل'
                            : 'Alert: Low Stock Products',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ...products
                      .where((p) => p.stock < 10)
                      .take(5)
                      .map(
                        (product) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text(
                            '• ${product.name}: ${product.stock} ${isRTL ? 'قطعة متبقية' : 'remaining'}',
                            style: GoogleFonts.cairo(fontSize: 14),
                          ),
                        ),
                      ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Products List
          Text(
            isRTL ? 'قائمة المنتجات' : 'Products List',
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          ...products
              .take(20)
              .map(
                (product) => Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          product.stock < 10
                              ? Colors.red.withValues(alpha: 0.1)
                              : const Color(0xFF4CAF50).withValues(alpha: 0.1),
                      child: FaIcon(
                        FontAwesomeIcons.box,
                        color:
                            product.stock < 10
                                ? Colors.red
                                : const Color(0xFF4CAF50),
                        size: 16,
                      ),
                    ),
                    title: Text(
                      product.name,
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                    subtitle: Text(
                      '${product.category} • ${isRTL ? 'المخزون' : 'Stock'}: ${product.stock}',
                      style: GoogleFonts.cairo(fontSize: 12),
                    ),
                    trailing: Text(
                      product.price.toStringAsFixed(2),
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4CAF50),
                      ),
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildCustomersReport(CustomerProvider customerProvider, bool isRTL) {
    // Show loading indicator if data is being loaded
    if (customerProvider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
        ),
      );
    }

    final customers = customerProvider.customers;
    final totalCustomers = customers.length;
    final totalDebt = customers.fold<double>(
      0,
      (sum, customer) => sum + customer.balance,
    );
    final customersWithDebt = customers.where((c) => c.balance > 0).length;

    // Show empty state if no customers
    if (customers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.users,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              isRTL ? 'لا يوجد عملاء' : 'No customers found',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isRTL ? 'ابدأ بإضافة عملاء جدد' : 'Start by adding new customers',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await customerProvider.loadCustomers();
      },
      color: const Color(0xFF4CAF50),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'إجمالي العملاء' : 'Total Customers',
                    value: totalCustomers.toString(),
                    icon: FontAwesomeIcons.users,
                    color: const Color(0xFF4CAF50),
                    isRTL: isRTL,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'إجمالي الديون' : 'Total Debt',
                    value: totalDebt.toStringAsFixed(2),
                    icon: FontAwesomeIcons.dollarSign,
                    color: Colors.red,
                    isRTL: isRTL,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildSummaryCard(
              title: isRTL ? 'عملاء مدينون' : 'Customers with Debt',
              value: customersWithDebt.toString(),
              icon: FontAwesomeIcons.triangleExclamation,
              color: Colors.orange,
              isRTL: isRTL,
            ),

            const SizedBox(height: 24),

            // Customers List
            Text(
              isRTL ? 'قائمة العملاء' : 'Customers List',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            ...customers
                .take(20)
                .map(
                  (customer) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor:
                            customer.balance > 0
                                ? Colors.red.withValues(alpha: 0.1)
                                : const Color(
                                  0xFF4CAF50,
                                ).withValues(alpha: 0.1),
                        child: FaIcon(
                          FontAwesomeIcons.user,
                          color:
                              customer.balance > 0
                                  ? Colors.red
                                  : const Color(0xFF4CAF50),
                          size: 16,
                        ),
                      ),
                      title: Text(
                        customer.name,
                        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                      ),
                      subtitle: Text(
                        customer.phone.isEmpty
                            ? (isRTL ? 'لا يوجد هاتف' : 'No phone')
                            : customer.phone,
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                      trailing: Text(
                        customer.balance.toStringAsFixed(2),
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.bold,
                          color:
                              customer.balance > 0
                                  ? Colors.red
                                  : const Color(0xFF4CAF50),
                        ),
                      ),
                    ),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitsReport(InvoiceProvider invoiceProvider, bool isRTL) {
    // Show loading indicator if data is being loaded
    if (invoiceProvider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
        ),
      );
    }

    final filteredInvoices =
        invoiceProvider.invoices.where((invoice) {
          return invoice.date.isAfter(
                _startDate.subtract(const Duration(days: 1)),
              ) &&
              invoice.date.isBefore(_endDate.add(const Duration(days: 1)));
        }).toList();

    final totalRevenue = filteredInvoices.fold<double>(
      0,
      (sum, invoice) => sum + invoice.finalAmount,
    );
    final totalCost = filteredInvoices.fold<double>(
      0,
      (sum, invoice) =>
          sum +
          invoice.items.fold<double>(
            0,
            (itemSum, item) => itemSum + (item.unitPrice * 0.7 * item.quantity),
          ),
    );
    final totalProfit = totalRevenue - totalCost;
    final profitMargin =
        totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0.0;

    // Show empty state if no invoices in date range
    if (filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.chartPie,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              isRTL ? 'لا توجد بيانات أرباح' : 'No profit data available',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                  ? 'لا توجد فواتير في الفترة المحددة'
                  : 'No invoices in selected date range',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await invoiceProvider.loadInvoices();
      },
      color: const Color(0xFF4CAF50),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'إجمالي الإيرادات' : 'Total Revenue',
                    value: totalRevenue.toStringAsFixed(2),
                    icon: FontAwesomeIcons.dollarSign,
                    color: const Color(0xFF4CAF50),
                    isRTL: isRTL,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'إجمالي التكلفة' : 'Total Cost',
                    value: totalCost.toStringAsFixed(2),
                    icon: FontAwesomeIcons.minus,
                    color: Colors.red,
                    isRTL: isRTL,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'صافي الربح' : 'Net Profit',
                    value: totalProfit.toStringAsFixed(2),
                    icon: FontAwesomeIcons.chartLine,
                    color:
                        totalProfit >= 0 ? const Color(0xFF4CAF50) : Colors.red,
                    isRTL: isRTL,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    title: isRTL ? 'هامش الربح' : 'Profit Margin',
                    value: '${profitMargin.toStringAsFixed(1)}%',
                    icon: FontAwesomeIcons.percent,
                    color: Colors.blue,
                    isRTL: isRTL,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Profit Analysis
            Text(
              isRTL ? 'تحليل الأرباح' : 'Profit Analysis',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                children: [
                  _buildProfitAnalysisRow(
                    isRTL ? 'إجمالي الإيرادات' : 'Total Revenue',
                    totalRevenue,
                    Colors.green,
                    isRTL,
                  ),
                  const Divider(),
                  _buildProfitAnalysisRow(
                    isRTL ? 'إجمالي التكلفة' : 'Total Cost',
                    totalCost,
                    Colors.red,
                    isRTL,
                  ),
                  const Divider(),
                  _buildProfitAnalysisRow(
                    isRTL ? 'صافي الربح' : 'Net Profit',
                    totalProfit,
                    totalProfit >= 0 ? Colors.green : Colors.red,
                    isRTL,
                    isBold: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required bool isRTL,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: FaIcon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitAnalysisRow(
    String label,
    double amount,
    Color color,
    bool isRTL, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
          Text(
            amount.toStringAsFixed(2),
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
