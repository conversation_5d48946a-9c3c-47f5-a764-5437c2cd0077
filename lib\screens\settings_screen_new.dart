import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/screens/language_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  bool _darkMode = false;
  bool _enableNotifications = true;
  String _languageValue = 'العربية';
  final String _currencyValue = 'دينار جزائري (DZD)';

  // Invoice settings
  bool _showLogo = true;
  bool _showTaxNumber = true;
  bool _showDiscountAmount = true;
  String _invoiceFooter = 'شكراً لتسوقكم معنا';

  // Printer settings
  bool _autoPrint = false;
  final String _printerType = 'thermal';
  final String _paperSize = '80mm';

  // Bluetooth settings
  bool _bluetoothEnabled = false;
  String? _selectedDevice;
  List<BluetoothDevice> _devices = [];

  // Tabs
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _checkBluetoothState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkBluetoothState() async {
    try {
      _bluetoothEnabled =
          await FlutterBluetoothSerial.instance.isEnabled ?? false;
      if (_bluetoothEnabled) {
        await _getDevices();
      }
      setState(() {});
    } catch (e) {
      print('Error checking Bluetooth state: $e');
    }
  }

  Future<void> _getDevices() async {
    try {
      _devices = await FlutterBluetoothSerial.instance.getBondedDevices();
      setState(() {});
    } catch (e) {
      print('Error getting Bluetooth devices: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocaleProvider>(
          builder:
              (context, localeProvider, _) =>
                  Text(localeProvider.isRTL ? 'الإعدادات' : 'Settings'),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: colorScheme.primary,
          labelColor: colorScheme.primary,
          unselectedLabelColor: colorScheme.onSurface.withValues(alpha: 0.7),
          tabs: [
            Tab(
              icon: const Icon(FontAwesomeIcons.store),
              text:
                  Provider.of<LocaleProvider>(context).isRTL
                      ? 'المتجر'
                      : 'Store',
            ),
            Tab(
              icon: const Icon(FontAwesomeIcons.fileInvoice),
              text:
                  Provider.of<LocaleProvider>(context).isRTL
                      ? 'الفاتورة'
                      : 'Invoice',
            ),
            Tab(
              icon: const Icon(FontAwesomeIcons.print),
              text:
                  Provider.of<LocaleProvider>(context).isRTL
                      ? 'الطباعة'
                      : 'Printing',
            ),
            Tab(
              icon: const Icon(FontAwesomeIcons.gear),
              text:
                  Provider.of<LocaleProvider>(context).isRTL
                      ? 'النظام'
                      : 'System',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Store Settings Tab
          _buildStoreSettingsTab(),

          // Invoice Settings Tab
          _buildInvoiceSettingsTab(),

          // Printing Settings Tab
          _buildPrintingSettingsTab(),

          // System Settings Tab
          _buildSystemSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildStoreSettingsTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        // Store Information Section
        _buildSectionHeader(context, 'معلومات المتجر'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.store,
          iconColor: const Color(0xFF4CAF50),
          title: 'اسم المتجر',
          subtitle: 'متجر التطبيق',
          onTap:
              () => _showEditTextDialog(
                context,
                'تعديل اسم المتجر',
                'متجر التطبيق',
              ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.phone,
          iconColor: const Color(0xFF2196F3),
          title: 'رقم الهاتف',
          subtitle: '+213 123456789',
          onTap:
              () => _showEditTextDialog(
                context,
                'تعديل رقم الهاتف',
                '+213 123456789',
              ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.locationDot,
          iconColor: const Color(0xFFF44336),
          title: 'العنوان',
          subtitle: 'الجزائر العاصمة، الجزائر',
          onTap:
              () => _showEditTextDialog(
                context,
                'تعديل العنوان',
                'الجزائر العاصمة، الجزائر',
              ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.idCard,
          iconColor: const Color(0xFF9C27B0),
          title: 'الرقم الضريبي',
          subtitle: '123456789',
          onTap:
              () => _showEditTextDialog(
                context,
                'تعديل الرقم الضريبي',
                '123456789',
              ),
        ),

        // Appearance Section
        _buildSectionHeader(context, 'المظهر'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.palette,
          iconColor: const Color(0xFF795548),
          title: 'سمة التطبيق',
          subtitle: _darkMode ? 'الوضع الداكن' : 'الوضع الفاتح',
          trailing: Switch(
            value: _darkMode,
            onChanged: (value) {
              setState(() {
                _darkMode = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),

        // Preferences Section
        _buildSectionHeader(context, 'التفضيلات'),
        Consumer<LocaleProvider>(
          builder:
              (context, localeProvider, _) => _buildModernSettingCard(
                context,
                icon: FontAwesomeIcons.language,
                iconColor: const Color(0xFF3F51B5),
                title: localeProvider.isRTL ? 'اللغة' : 'Language',
                subtitle: localeProvider.isRTL ? 'العربية' : 'English',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LanguageSettingsScreen(),
                    ),
                  );
                },
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        FontAwesomeIcons.rightLeft,
                        color: colorScheme.primary,
                        size: 18,
                      ),
                      onPressed: () {
                        localeProvider.toggleDirection();
                        setState(() {
                          _languageValue =
                              localeProvider.isRTL ? 'العربية' : 'English';
                        });
                      },
                      tooltip:
                          localeProvider.isRTL
                              ? 'تبديل إلى الإنجليزية'
                              : 'Switch to Arabic',
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: colorScheme.primary,
                    ),
                  ],
                ),
              ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.moneyBill,
          iconColor: const Color(0xFF4CAF50),
          title: 'العملة',
          subtitle: _currencyValue,
          onTap: () {
            _showCurrencyDialog(context);
          },
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.bell,
          iconColor: const Color(0xFFFF9800),
          title: 'الإشعارات',
          subtitle: _enableNotifications ? 'مفعلة' : 'غير مفعلة',
          trailing: Switch(
            value: _enableNotifications,
            onChanged: (value) {
              setState(() {
                _enableNotifications = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceSettingsTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildSectionHeader(context, 'إعدادات الفاتورة'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.image,
          iconColor: const Color(0xFF2196F3),
          title: 'إظهار شعار المتجر',
          subtitle: _showLogo ? 'مفعل' : 'غير مفعل',
          trailing: Switch(
            value: _showLogo,
            onChanged: (value) {
              setState(() {
                _showLogo = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.idCard,
          iconColor: const Color(0xFF9C27B0),
          title: 'إظهار الرقم الضريبي',
          subtitle: _showTaxNumber ? 'مفعل' : 'غير مفعل',
          trailing: Switch(
            value: _showTaxNumber,
            onChanged: (value) {
              setState(() {
                _showTaxNumber = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.percent,
          iconColor: const Color(0xFFF44336),
          title: 'إظهار مبلغ الخصم',
          subtitle: _showDiscountAmount ? 'مفعل' : 'غير مفعل',
          trailing: Switch(
            value: _showDiscountAmount,
            onChanged: (value) {
              setState(() {
                _showDiscountAmount = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.pen,
          iconColor: const Color(0xFF795548),
          title: 'تذييل الفاتورة',
          subtitle: _invoiceFooter,
          onTap:
              () => _showEditTextDialog(
                context,
                'تعديل تذييل الفاتورة',
                _invoiceFooter,
                onSave: (value) {
                  setState(() {
                    _invoiceFooter = value;
                  });
                },
              ),
        ),
      ],
    );
  }

  Widget _buildPrintingSettingsTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildSectionHeader(context, 'إعدادات الطباعة'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.print,
          iconColor: const Color(0xFF607D8B),
          title: 'الطباعة التلقائية بعد الدفع',
          subtitle: _autoPrint ? 'مفعلة' : 'غير مفعلة',
          trailing: Switch(
            value: _autoPrint,
            onChanged: (value) {
              setState(() {
                _autoPrint = value;
              });
            },
            activeColor: colorScheme.primary,
          ),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.receipt,
          iconColor: const Color(0xFF00BCD4),
          title: 'نوع الطابعة',
          subtitle: _printerType == 'thermal' ? 'طابعة حرارية' : 'طابعة عادية',
          onTap: () => _showPrinterTypeDialog(context),
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.ruler,
          iconColor: const Color(0xFF3F51B5),
          title: 'حجم الورق',
          subtitle: _paperSize,
          onTap: () => _showPaperSizeDialog(context),
        ),

        // Bluetooth Settings
        _buildSectionHeader(context, 'إعدادات البلوتوث'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.bluetooth,
          iconColor: const Color(0xFF2196F3),
          title: 'البلوتوث',
          subtitle: _bluetoothEnabled ? 'مفعل' : 'غير مفعل',
          trailing: Switch(
            value: _bluetoothEnabled,
            onChanged: (value) async {
              if (value) {
                await FlutterBluetoothSerial.instance.requestEnable();
              } else {
                await FlutterBluetoothSerial.instance.requestDisable();
              }
              await _checkBluetoothState();
            },
            activeColor: colorScheme.primary,
          ),
        ),
        if (_bluetoothEnabled) ...[
          _buildModernSettingCard(
            context,
            icon: FontAwesomeIcons.print,
            iconColor: const Color(0xFF4CAF50),
            title: 'اختيار الطابعة',
            subtitle: _selectedDevice ?? 'لم يتم اختيار طابعة',
            onTap: () => _showBluetoothDevicesDialog(context),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              await _getDevices();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث قائمة الأجهزة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSystemSettingsTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildSectionHeader(context, 'النظام'),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.database,
          iconColor: const Color(0xFF4CAF50),
          title: 'النسخ الاحتياطي واستعادة البيانات',
          subtitle: 'حفظ واستعادة بيانات التطبيق',
          onTap: () {
            // Backup/restore options
          },
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.broom,
          iconColor: const Color(0xFFF44336),
          title: 'مسح ذاكرة التخزين المؤقت',
          subtitle: 'تحسين أداء التطبيق',
          onTap: () {
            // Clear cache
            _showConfirmDialog(
              context,
              'مسح ذاكرة التخزين المؤقت',
              'هل أنت متأكد من مسح ذاكرة التخزين المؤقت؟',
            );
          },
        ),
        _buildModernSettingCard(
          context,
          icon: FontAwesomeIcons.circleInfo,
          iconColor: const Color(0xFF2196F3),
          title: 'حول التطبيق',
          subtitle: 'الإصدار 1.0.0',
          onTap: () {
            // Show about dialog
            _showAboutDialog(context);
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(top: 24.0, bottom: 12.0),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: colorScheme.primary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSettingCard(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: iconColor, size: 22),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        subtitle:
            subtitle != null
                ? Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                )
                : null,
        trailing:
            trailing ??
            (onTap != null
                ? Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: colorScheme.primary,
                )
                : null),
        onTap: onTap,
      ),
    );
  }

  void _showEditTextDialog(
    BuildContext context,
    String title,
    String initialValue, {
    Function(String)? onSave,
  }) {
    final controller = TextEditingController(text: initialValue);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(border: OutlineInputBorder()),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (onSave != null) {
                    onSave(controller.text);
                  }
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _showCurrencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر العملة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('دينار جزائري (DZD)'),
                  value: 'دينار جزائري (DZD)',
                  groupValue: _currencyValue,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('دولار أمريكي (USD)'),
                  value: 'دولار أمريكي (USD)',
                  groupValue: _currencyValue,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('يورو (EUR)'),
                  value: 'يورو (EUR)',
                  groupValue: _currencyValue,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showPrinterTypeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر نوع الطابعة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('طابعة حرارية'),
                  value: 'thermal',
                  groupValue: _printerType,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('طابعة عادية'),
                  value: 'normal',
                  groupValue: _printerType,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showPaperSizeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر حجم الورق'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('80mm'),
                  value: '80mm',
                  groupValue: _paperSize,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('58mm'),
                  value: '58mm',
                  groupValue: _paperSize,
                  onChanged: (value) {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showBluetoothDevicesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر الطابعة'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _devices.length,
                itemBuilder: (context, index) {
                  final device = _devices[index];
                  return ListTile(
                    title: Text(device.name ?? 'Unknown Device'),
                    subtitle: Text(device.address),
                    onTap: () {
                      setState(() {
                        _selectedDevice = device.name;
                      });
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showConfirmDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Perform action here
                },
                child: const Text('تأكيد'),
              ),
            ],
          ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حول التطبيق'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('تطبيق نقاط البيع'),
                SizedBox(height: 8),
                Text('الإصدار: 1.0.0'),
                SizedBox(height: 8),
                Text('تطوير: فريق التطوير'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }
}
