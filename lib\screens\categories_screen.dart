import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/models/category.dart';
import 'package:pos_app/widgets/category_widgets.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});
super.
  @override
  _CategoriesScreenState createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  bool _isGridView = true;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'الفئات' : 'Categories',
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            tooltip: _isGridView 
                ? (isRTL ? 'عرض قائمة' : 'List View') 
                : (isRTL ? 'عرض شبكة' : 'Grid View'),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: isRTL ? 'بحث عن الفئات...' : 'Search categories...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Categories List/Grid
          Expanded(
            child: Consumer<CategoryProvider>(
              builder: (ctx, categoryProvider, _) {
                final categories = categoryProvider.categories
                    .where((category) => 
                        _searchQuery.isEmpty ||
                        category.name.toLowerCase().contains(_searchQuery.toLowerCase()))
                    .toList();
                final isLoading = categoryProvider.isLoading;

                if (isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (categories.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.category_outlined,
                          size: 64,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isNotEmpty
                              ? (isRTL ? 'لا توجد نتائج للبحث' : 'No search results')
                              : (isRTL ? 'لا توجد فئات' : 'No categories found'),
                          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _showAddCategoryDialog(context),
                          icon: const Icon(Icons.add),
                          label: Text(isRTL ? 'إضافة فئة' : 'Add Category'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => categoryProvider.loadCategories(),
                  child: _isGridView
                      ? GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio: 0.85,
                          ),
                          itemCount: categories.length,
                          itemBuilder: (ctx, index) {
                            final category = categories[index];
                            return CategoryCard(
                              category: category,
                              onTap: () {
                                // Navigate to products filtered by this category
                              },
                              onEdit: () => _showEditCategoryDialog(context, category),
                              onDelete: () => _showDeleteCategoryDialog(context, category),
                            );
                          },
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: categories.length,
                          itemBuilder: (ctx, index) {
                            final category = categories[index];
                            return _buildCategoryListItem(context, category);
                          },
                        ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(context),
        tooltip: isRTL ? 'إضافة فئة جديدة' : 'Add New Cat
      ),
        child: const Icon(Icons.add),
    );
  }

  Widget _buildCategoryListItem(BuildContext context, Category category) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    IconData iconData = Icons.category;
    
    if (category.iconName != null) {
      switch (category.iconName) {
        case 'local_cafe': iconData = Icons.local_cafe; break;
        case 'bakery_dining': iconData = Icons.bakery_dining; break;
        case 'lunch_dining': iconData = Icons.lunch_dining; break;
        case 'restaurant': iconData = Icons.restaurant; break;
        case 'egg': iconData = Icons.egg_alt; break;
        case 'eco': iconData = Icons.eco; break;
        case 'shopping_bag': iconData = Icons.shopping_bag; break;
        case 'checkroom': iconData = Icons.checkroom; break;
        case 'devices': iconData = Icons.devices; break;
        case 'book': iconData = Icons.book; break;
        case 'sports_soccer': iconData = Icons.sports_soccer; break;
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            shape: BoxShape.circle,
          ),
          child: Icon(iconData, color: theme.colorScheme.primary),
        ),
        title: Text(
          category.name,
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          category.description ?? (isRTL ? '${category.productCount ?? 0} منتج' : '${category.productCount ?? 0} Products'),
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()).copyWith(
            fontSize: 12,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _showEditCategoryDialog(context, category),
              color: theme.colorScheme.primary,
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _showDeleteCategoryDialog(context, category),
              color: theme.colorScheme.error,
            ),
          ],
        ),
        onTap: () {
          // Navigate to products filtered by this category
        },
      ),
    );
  }

  void _showAddCategoryDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedIcon = 'category';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إضافة فئة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الفئة',
                    hintText: 'أدخل اسم الفئة',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'أدخل الوصف',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                _buildIconSelector((icon) {
                  setState(() {
                    selectedIcon = icon;
                  });
                }, initialSelection: selectedIcon),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final newCategory = Category(
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim(),
                    iconName: selectedIcon,
                  );
                  Provider.of<CategoryProvider>(context, listen: false)
                      .addCategory(newCategory);
                  Navigator.of(ctx).pop();
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditCategoryDialog(BuildContext context, Category category) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تعديل الفئة'
              : 'Edit Category',
        ),
        content: SingleChildScrollView(
          child: CategoryForm(
            category: category,
            onSave: (updatedCategory) {
              Provider.of<CategoryProvider>(context, listen: false)
                  .updateCategory(updatedCategory);
              Navigator.of(ctx).pop();
            },
            onCancel: () => Navigator.of(ctx).pop(),
            isDialog: true,
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
        actionsPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  void _showDeleteCategoryDialog(BuildContext context, Category category) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'حذف الفئة' : 'Delete Category'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من رغبتك في حذف "${category.name}"؟ المنتجات في هذه الفئة ستبقى ولكن لن تكون مرتبطة بأي فئة.'
              : 'Are you sure you want to delete "${category.name}"? Products in this category will remain but will have no category.',
          style: (isRTL ? GoogleFonts.cairo() : GoogleFonts.poppins()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<CategoryProvider>(context, listen: false)
                  .deleteCategory(category.id!);
              Navigator.of(ctx).pop();
            },
            child: Text(
              isRTL ? 'حذف' : 'Delete',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  Widget _buildIconSelector(Function(String) onIconSelected, {String? initialSelection}) {
    final theme = Theme.of(context);
    final icons = {
      'category': Icons.category,
      'local_cafe': Icons.local_cafe,
      'bakery_dining': Icons.bakery_dining,
      'lunch_dining': Icons.lunch_dining,
      'restaurant': Icons.restaurant,
      'egg': Icons.egg_alt,
      'eco': Icons.eco,
      'shopping_bag': Icons.shopping_bag,
      'checkroom': Icons.checkroom,
      'devices': Icons.devices,
      'book': Icons.book,
      'sports_soccer': Icons.sports_soccer,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'اختر الأيقونة:'
              : 'Select Icon:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 60,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: icons.entries.map((entry) {
              final isSelected = initialSelection == entry.key;
              return InkWell(
                onTap: () => onIconSelected(entry.key),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? theme.colorScheme.primaryContainer 
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected 
                          ? theme.colorScheme.primary 
                          : Colors.grey.shade300,
                    ),
                  ),
                  margin: const EdgeInsets.only(right: 8),
                  child: Icon(entry.value),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}